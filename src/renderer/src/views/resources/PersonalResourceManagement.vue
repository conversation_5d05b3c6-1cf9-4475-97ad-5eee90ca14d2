<template>
  <div class="personal-resource-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <User class="title-icon" />
          个人资源管理
        </h1>
        <p class="page-description">管理您的个人主机配置、密钥链和代码片段</p>
      </div>
      <div class="header-actions">
        <a-button
          type="primary"
          @click="showAddResourceModal"
        >
          <Plus class="btn-icon" />
          添加资源
        </a-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-row">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索资源名称、IP地址或标签"
          class="search-input custom-search-input"
          @search="handleSearch"
          @input="handleSearch"
        />
        <a-select
          v-model:value="selectedType"
          placeholder="资源类型"
          class="filter-select"
          @change="handleTypeFilter"
        >
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option value="host">主机配置</a-select-option>
          <a-select-option value="keychain">密钥链</a-select-option>
          <a-select-option value="snippet">代码片段</a-select-option>
        </a-select>
        <a-select
          v-model:value="selectedEnvironment"
          placeholder="环境"
          class="filter-select"
          @change="handleEnvironmentFilter"
        >
          <a-select-option value="">全部环境</a-select-option>
          <a-select-option value="production">生产环境</a-select-option>
          <a-select-option value="staging">测试环境</a-select-option>
          <a-select-option value="development">开发环境</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 资源列表 -->
    <div class="resource-list">
      <div class="list-header">
        <span class="resource-count">共 {{ filteredResources.length }} 个资源</span>
        <div class="view-controls">
          <a-radio-group
            v-model:value="viewMode"
            button-style="solid"
            size="small"
          >
            <a-radio-button value="card">卡片视图</a-radio-button>
            <a-radio-button value="table">表格视图</a-radio-button>
          </a-radio-group>
        </div>
      </div>

      <!-- 卡片视图 -->
      <div
        v-if="viewMode === 'card'"
        class="card-view"
      >
        <div class="resource-grid">
          <div
            v-for="resource in filteredResources"
            :key="resource.id"
            class="resource-card"
            @click="selectResource(resource)"
          >
            <div class="card-header">
              <div class="resource-info">
                <div class="resource-icon">
                  <Server v-if="resource.type === 'host'" />
                  <Key v-else-if="resource.type === 'keychain'" />
                  <Code v-else />
                </div>
                <div class="resource-details">
                  <h3 class="resource-name">{{ resource.name }}</h3>
                  <p class="resource-description">{{ resource.description || '暂无描述' }}</p>
                </div>
              </div>
              <div class="card-actions">
                <a-dropdown>
                  <a-button
                    type="text"
                    size="small"
                  >
                    <MoreHorizontal class="action-icon" />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editResource(resource)">
                        <Edit class="menu-icon" />
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="duplicateResource(resource)">
                        <Copy class="menu-icon" />
                        复制
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        class="danger-item"
                        @click="deleteResource(resource)"
                      >
                        <Trash2 class="menu-icon" />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <div class="card-content">
              <div class="resource-meta">
                <a-tag :color="getTypeColor(resource.type)">{{ getTypeLabel(resource.type) }}</a-tag>
                <a-tag :color="getEnvironmentColor(resource.environment)">{{ resource.environment }}</a-tag>
              </div>
              <div class="resource-tags">
                <a-tag
                  v-for="tag in resource.tags"
                  :key="tag"
                  size="small"
                  >{{ tag }}</a-tag
                >
              </div>
            </div>
            <div class="card-footer">
              <span class="update-time">更新于 {{ formatTime(resource.updated_at) }}</span>
              <div class="sync-status">
                <a-badge
                  :status="getSyncStatusType(resource.sync_status)"
                  :text="getSyncStatusText(resource.sync_status)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div
        v-else
        class="table-view"
      >
        <a-table
          :columns="tableColumns"
          :data-source="filteredResources"
          :pagination="tablePagination"
          :loading="loading"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="name-cell">
                <div class="resource-icon">
                  <Server v-if="record.type === 'host'" />
                  <Key v-else-if="record.type === 'keychain'" />
                  <Code v-else />
                </div>
                <div class="name-info">
                  <span class="name-text">{{ record.name }}</span>
                  <span class="description-text">{{ record.description || '暂无描述' }}</span>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">{{ getTypeLabel(record.type) }}</a-tag>
            </template>
            <template v-else-if="column.key === 'environment'">
              <a-tag :color="getEnvironmentColor(record.environment)">{{ record.environment }}</a-tag>
            </template>
            <template v-else-if="column.key === 'tags'">
              <div class="tags-cell">
                <a-tag
                  v-for="tag in record.tags.slice(0, 3)"
                  :key="tag"
                  size="small"
                  >{{ tag }}</a-tag
                >
                <span
                  v-if="record.tags.length > 3"
                  class="more-tags"
                  >+{{ record.tags.length - 3 }}</span
                >
              </div>
            </template>
            <template v-else-if="column.key === 'sync_status'">
              <a-badge
                :status="getSyncStatusType(record.sync_status)"
                :text="getSyncStatusText(record.sync_status)"
              />
            </template>
            <template v-else-if="column.key === 'updated_at'">
              {{ formatTime(record.updated_at) }}
            </template>
            <template v-else-if="column.key === 'actions'">
              <div class="action-buttons">
                <a-button
                  type="text"
                  size="small"
                  @click="editResource(record)"
                >
                  <Edit class="action-icon" />
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  @click="duplicateResource(record)"
                >
                  <Copy class="action-icon" />
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  danger
                  @click="deleteResource(record)"
                >
                  <Trash2 class="action-icon" />
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 添加/编辑资源模态框 -->
    <a-modal
      v-model:open="showResourceModal"
      :title="editingResource ? '编辑资源' : '添加资源'"
      width="800px"
      @ok="handleSaveResource"
      @cancel="handleCancelResource"
    >
      <a-form
        ref="resourceForm"
        :model="resourceFormData"
        :rules="resourceFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="资源名称"
              name="name"
            >
              <a-input
                v-model:value="resourceFormData.name"
                placeholder="请输入资源名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="资源类型"
              name="type"
            >
              <a-select
                v-model:value="resourceFormData.type"
                placeholder="请选择资源类型"
              >
                <a-select-option value="host">主机配置</a-select-option>
                <a-select-option value="keychain">密钥链</a-select-option>
                <a-select-option value="snippet">代码片段</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="环境"
              name="environment"
            >
              <a-select
                v-model:value="resourceFormData.environment"
                placeholder="请选择环境"
              >
                <a-select-option value="production">生产环境</a-select-option>
                <a-select-option value="staging">测试环境</a-select-option>
                <a-select-option value="development">开发环境</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="标签"
              name="tags"
            >
              <a-select
                v-model:value="resourceFormData.tags"
                mode="tags"
                placeholder="请输入标签"
                :token-separators="[',']"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="resourceFormData.description"
            placeholder="请输入资源描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item
          label="配置数据"
          name="config_data"
        >
          <a-textarea
            v-model:value="resourceFormData.config_data"
            placeholder="请输入JSON格式的配置数据"
            :rows="6"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, notification } from 'ant-design-vue'
import { User, Plus, Server, Key, Code, MoreHorizontal, Edit, Copy, Trash2 } from 'lucide-vue-next'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 个人资源管理页面
 * 功能：管理个人主机配置、密钥链和代码片段
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedType = ref('')
const selectedEnvironment = ref('')
const viewMode = ref('card')
const showResourceModal = ref(false)
const editingResource = ref(null)
const selectedResource = ref(null)

// 资源列表数据
const resources = ref([
  {
    id: '1',
    name: 'Web服务器-01',
    type: 'host',
    environment: 'production',
    description: '主要的Web服务器',
    tags: ['nginx', 'php', 'mysql'],
    config_data: {
      ip: '*************',
      port: 22,
      username: 'admin'
    },
    sync_status: 'synced',
    updated_at: new Date('2025-01-15T10:30:00Z')
  },
  {
    id: '2',
    name: 'SSH密钥-生产环境',
    type: 'keychain',
    environment: 'production',
    description: '生产环境SSH密钥',
    tags: ['ssh', 'production'],
    config_data: {
      private_key: '-----BEGIN PRIVATE KEY-----\n...',
      public_key: 'ssh-rsa AAAAB3...'
    },
    sync_status: 'pending',
    updated_at: new Date('2025-01-15T09:15:00Z')
  }
])

// 表单数据
const resourceFormData = reactive({
  name: '',
  type: '',
  environment: '',
  description: '',
  tags: [],
  config_data: ''
})

// 表单验证规则
const resourceFormRules = {
  name: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
  environment: [{ required: true, message: '请选择环境', trigger: 'change' }]
}

// 表格列配置
const tableColumns = [
  {
    title: '名称',
    key: 'name',
    width: 200
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '环境',
    key: 'environment',
    width: 100
  },
  {
    title: '标签',
    key: 'tags',
    width: 150
  },
  {
    title: '同步状态',
    key: 'sync_status',
    width: 120
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 表格分页配置
const tablePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 计算属性
const filteredResources = computed(() => {
  let filtered = resources.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (resource) =>
        resource.name.toLowerCase().includes(keyword) ||
        resource.description?.toLowerCase().includes(keyword) ||
        resource.tags.some((tag) => tag.toLowerCase().includes(keyword))
    )
  }

  // 类型过滤
  if (selectedType.value) {
    filtered = filtered.filter((resource) => resource.type === selectedType.value)
  }

  // 环境过滤
  if (selectedEnvironment.value) {
    filtered = filtered.filter((resource) => resource.environment === selectedEnvironment.value)
  }

  return filtered
})

// 方法
const formatTime = (date: Date) => {
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const getTypeColor = (type: string) => {
  const colors = {
    host: 'blue',
    keychain: 'green',
    snippet: 'orange'
  }
  return colors[type] || 'default'
}

const getTypeLabel = (type: string) => {
  const labels = {
    host: '主机配置',
    keychain: '密钥链',
    snippet: '代码片段'
  }
  return labels[type] || type
}

const getEnvironmentColor = (environment: string) => {
  const colors = {
    production: 'red',
    staging: 'orange',
    development: 'green'
  }
  return colors[environment] || 'default'
}

const getSyncStatusType = (status: string) => {
  const types = {
    synced: 'success',
    pending: 'processing',
    failed: 'error'
  }
  return types[status] || 'default'
}

const getSyncStatusText = (status: string) => {
  const texts = {
    synced: '已同步',
    pending: '同步中',
    failed: '同步失败'
  }
  return texts[status] || status
}

const handleSearch = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredResources 计算属性中处理
  console.log('搜索个人资源:', searchKeyword.value)
}

const handleTypeFilter = () => {
  // 类型过滤逻辑已在计算属性中处理
}

const handleEnvironmentFilter = () => {
  // 环境过滤逻辑已在计算属性中处理
}

const selectResource = (resource: any) => {
  selectedResource.value = resource
}

const showAddResourceModal = () => {
  editingResource.value = null
  resetResourceForm()
  showResourceModal.value = true
}

const editResource = (resource: any) => {
  editingResource.value = resource
  Object.assign(resourceFormData, {
    ...resource,
    config_data: JSON.stringify(resource.config_data, null, 2)
  })
  showResourceModal.value = true
}

const duplicateResource = (resource: any) => {
  editingResource.value = null
  Object.assign(resourceFormData, {
    ...resource,
    name: `${resource.name} (副本)`,
    config_data: JSON.stringify(resource.config_data, null, 2)
  })
  showResourceModal.value = true
}

const deleteResource = (resource: any) => {
  // 实现删除逻辑
  message.success('资源删除成功')
}

const resetResourceForm = () => {
  Object.assign(resourceFormData, {
    name: '',
    type: '',
    environment: '',
    description: '',
    tags: [],
    config_data: ''
  })
}

const handleSaveResource = () => {
  // 实现保存逻辑
  showResourceModal.value = false
  message.success(editingResource.value ? '资源更新成功' : '资源创建成功')
}

const handleCancelResource = () => {
  showResourceModal.value = false
  resetResourceForm()
}

const handleTableChange = (pagination: any) => {
  tablePagination.current = pagination.current
  tablePagination.pageSize = pagination.pageSize
}

// 生命周期
onMounted(() => {
  // 初始化数据
  tablePagination.total = resources.value.length
})
</script>

<style scoped>
.personal-resource-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  flex: 1;
  max-width: 400px;
}

/* 搜索框样式修复 - 使用CSS变量适配主题 */
.custom-search-input .ant-input {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  border: 1px solid var(--border-color, #d9d9d9) !important;
  color: var(--text-color, #000000) !important;
}

.custom-search-input .ant-input:focus {
  border-color: var(--input-focus-border, #1890ff) !important;
  box-shadow: var(--input-focus-shadow, 0 0 0 2px rgba(24, 144, 255, 0.2)) !important;
}

.custom-search-input .ant-input::placeholder {
  color: var(--text-color-tertiary, #bfbfbf) !important;
}

.custom-search-input .ant-input-search-button {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  border-color: var(--border-color, #d9d9d9) !important;
  color: var(--text-color, #000000) !important;
}

/* 额外的强制样式覆盖 */
.custom-search-input .ant-input-search .ant-input-affix-wrapper,
.custom-search-input .ant-input-search .ant-input {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  color: var(--text-color, #000000) !important;
  border-color: var(--border-color, #d9d9d9) !important;
}

.filter-select {
  width: 150px;
}

.resource-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.resource-count {
  color: #6b7280;
  font-size: 14px;
}

.card-view {
  padding: 24px;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.resource-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.resource-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.resource-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.resource-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  margin-right: 12px;
  color: #1890ff;
}

.resource-details {
  flex: 1;
}

.resource-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.resource-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.card-actions {
  margin-left: 12px;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.card-content {
  margin-bottom: 12px;
}

.resource-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.update-time {
  color: #9ca3af;
  font-size: 12px;
}

.table-view {
  padding: 0;
}

.name-cell {
  display: flex;
  align-items: center;
}

.name-cell .resource-icon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.name-info {
  display: flex;
  flex-direction: column;
}

.name-text {
  font-weight: 500;
  color: #1f2937;
}

.description-text {
  font-size: 12px;
  color: #6b7280;
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-tags {
  color: #6b7280;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.menu-icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.danger-item {
  color: #ef4444;
}
</style>
