<template>
  <div
    class="network-devices-monitor"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Router class="title-icon" />
            网络设备监控
          </h1>
          <p class="page-description">监控交换机、路由器、防火墙等网络设备的运行状态</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="addDevice"
        >
          <Plus class="btn-icon" />
          添加设备
        </a-button>
      </div>
    </div>

    <!-- 设备概览 -->
    <div class="devices-overview">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-icon-wrapper switch">
            <Network class="card-icon" />
          </div>
          <div class="card-content">
            <h3>交换机</h3>
            <div class="device-count">{{ deviceStats.switches.total }}</div>
            <div class="device-status">
              <span class="online">{{ deviceStats.switches.online }} 在线</span>
              <span class="offline">{{ deviceStats.switches.offline }} 离线</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon-wrapper router">
            <Router class="card-icon" />
          </div>
          <div class="card-content">
            <h3>路由器</h3>
            <div class="device-count">{{ deviceStats.routers.total }}</div>
            <div class="device-status">
              <span class="online">{{ deviceStats.routers.online }} 在线</span>
              <span class="offline">{{ deviceStats.routers.offline }} 离线</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon-wrapper firewall">
            <Shield class="card-icon" />
          </div>
          <div class="card-content">
            <h3>防火墙</h3>
            <div class="device-count">{{ deviceStats.firewalls.total }}</div>
            <div class="device-status">
              <span class="online">{{ deviceStats.firewalls.online }} 在线</span>
              <span class="offline">{{ deviceStats.firewalls.offline }} 离线</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-icon-wrapper ap">
            <Wifi class="card-icon" />
          </div>
          <div class="card-content">
            <h3>无线AP</h3>
            <div class="device-count">{{ deviceStats.accessPoints.total }}</div>
            <div class="device-status">
              <span class="online">{{ deviceStats.accessPoints.online }} 在线</span>
              <span class="offline">{{ deviceStats.accessPoints.offline }} 离线</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备列表 -->
    <div class="devices-list">
      <div class="list-header">
        <h2 class="section-title">设备列表</h2>
        <div class="list-filters">
          <a-select
            v-model:value="selectedType"
            style="width: 120px"
            @change="filterDevices"
          >
            <a-select-option value="all">全部设备</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedStatus"
            style="width: 100px"
            @change="filterDevices"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="warning">警告</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索设备名称或IP"
            style="width: 200px"
            class="custom-search-input"
            @search="filterDevices"
            @input="filterDevices"
          />
        </div>
      </div>

      <div class="devices-grid">
        <div
          v-for="device in filteredDevices"
          :key="device.id"
          class="device-card"
          :class="device.status"
          @click="viewDeviceDetails(device)"
        >
          <div class="device-header">
            <div class="device-icon">
              <component
                :is="getDeviceIcon(device.type)"
                class="icon"
              />
            </div>
            <div
              class="device-status-indicator"
              :class="device.status"
            ></div>
          </div>
          <div class="device-info">
            <h3 class="device-name">{{ device.name }}</h3>
            <p class="device-ip">{{ device.ip }}</p>
            <p class="device-type">{{ getDeviceTypeLabel(device.type) }}</p>
          </div>
          <div class="device-metrics">
            <div class="metric">
              <span class="metric-label">CPU</span>
              <span class="metric-value">{{ device.cpu }}%</span>
            </div>
            <div class="metric">
              <span class="metric-label">内存</span>
              <span class="metric-value">{{ device.memory }}%</span>
            </div>
            <div class="metric">
              <span class="metric-label">延迟</span>
              <span class="metric-value">{{ device.latency }}ms</span>
            </div>
          </div>
          <div class="device-actions">
            <a-button
              size="small"
              @click.stop="configureDevice(device)"
            >
              <Settings class="btn-icon" />
              配置
            </a-button>
            <a-button
              size="small"
              @click.stop="monitorDevice(device)"
            >
              <Activity class="btn-icon" />
              监控
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <a-modal
      v-model:open="showAddDeviceModal"
      title="添加网络设备"
      width="600px"
      :footer="null"
    >
      <a-form
        :model="deviceForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveDevice"
      >
        <a-form-item
          label="设备名称"
          name="name"
          :rules="[{ required: true, message: '请输入设备名称' }]"
        >
          <a-input
            v-model:value="deviceForm.name"
            placeholder="请输入设备名称"
          />
        </a-form-item>

        <a-form-item
          label="设备类型"
          name="type"
          :rules="[{ required: true, message: '请选择设备类型' }]"
        >
          <a-select
            v-model:value="deviceForm.type"
            placeholder="请选择设备类型"
          >
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="IP地址"
          name="ip"
          :rules="[
            { required: true, message: '请输入IP地址' },
            { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' }
          ]"
        >
          <a-input
            v-model:value="deviceForm.ip"
            placeholder="请输入IP地址"
          />
        </a-form-item>

        <a-form-item
          label="端口"
          name="port"
        >
          <a-input-number
            v-model:value="deviceForm.port"
            :min="1"
            :max="65535"
            placeholder="端口号"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="用户名"
          name="username"
        >
          <a-input
            v-model:value="deviceForm.username"
            placeholder="管理用户名"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
        >
          <a-input-password
            v-model:value="deviceForm.password"
            placeholder="管理密码"
          />
        </a-form-item>

        <a-form-item
          label="SNMP团体名"
          name="snmpCommunity"
        >
          <a-input
            v-model:value="deviceForm.snmpCommunity"
            placeholder="SNMP Community"
          />
        </a-form-item>

        <a-form-item
          label="位置"
          name="location"
        >
          <a-input
            v-model:value="deviceForm.location"
            placeholder="设备物理位置"
          />
        </a-form-item>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="deviceForm.description"
            :rows="3"
            placeholder="设备描述信息"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeAddDeviceModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Router, RefreshCw, Plus, Network, Shield, Wifi, Settings, Activity } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')
const searchText = ref('')
const showAddDeviceModal = ref(false)
const saving = ref(false)

// 设备表单数据
const deviceForm = ref({
  name: '',
  type: '',
  ip: '',
  port: 22,
  username: '',
  password: '',
  snmpCommunity: 'public',
  location: '',
  description: ''
})

// 设备统计数据
const deviceStats = reactive({
  switches: { total: 12, online: 11, offline: 1 },
  routers: { total: 8, online: 8, offline: 0 },
  firewalls: { total: 4, online: 4, offline: 0 },
  accessPoints: { total: 24, online: 22, offline: 2 }
})

// 设备列表数据
const devices = ref([
  {
    id: 1,
    name: 'Core-Switch-01',
    ip: '************',
    type: 'switch',
    status: 'online',
    cpu: 25,
    memory: 45,
    latency: 2
  },
  {
    id: 2,
    name: 'Edge-Router-01',
    ip: '***********',
    type: 'router',
    status: 'online',
    cpu: 15,
    memory: 32,
    latency: 1
  },
  {
    id: 3,
    name: 'Firewall-Main',
    ip: '*************',
    type: 'firewall',
    status: 'online',
    cpu: 35,
    memory: 58,
    latency: 3
  },
  {
    id: 4,
    name: 'AP-Floor1-01',
    ip: '************0',
    type: 'ap',
    status: 'warning',
    cpu: 45,
    memory: 67,
    latency: 8
  },
  {
    id: 5,
    name: 'Access-Switch-02',
    ip: '************',
    type: 'switch',
    status: 'offline',
    cpu: 0,
    memory: 0,
    latency: 999
  }
])

// 过滤后的设备列表
const filteredDevices = computed(() => {
  let filtered = devices.value

  if (selectedType.value !== 'all') {
    filtered = filtered.filter((device) => device.type === selectedType.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((device) => device.status === selectedStatus.value)
  }

  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter((device) => device.name.toLowerCase().includes(search) || device.ip.includes(search))
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const addDevice = () => {
  showAddDeviceModal.value = true
  resetDeviceForm()
}

const closeAddDeviceModal = () => {
  showAddDeviceModal.value = false
  resetDeviceForm()
}

const resetDeviceForm = () => {
  deviceForm.value = {
    name: '',
    type: '',
    ip: '',
    port: 22,
    username: '',
    password: '',
    snmpCommunity: 'public',
    location: '',
    description: ''
  }
}

const saveDevice = async () => {
  saving.value = true

  try {
    // 模拟保存设备
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建新设备对象
    const newDevice = {
      id: Date.now(),
      name: deviceForm.value.name,
      ip: deviceForm.value.ip,
      type: deviceForm.value.type,
      status: 'online',
      cpu: Math.floor(Math.random() * 50) + 10,
      memory: Math.floor(Math.random() * 60) + 20,
      latency: Math.floor(Math.random() * 10) + 1,
      location: deviceForm.value.location,
      description: deviceForm.value.description
    }

    // 添加到设备列表
    devices.value.push(newDevice)

    // 更新统计数据
    updateDeviceStats()

    // 关闭对话框
    closeAddDeviceModal()

    // 显示成功消息
    console.log('设备添加成功:', newDevice)
  } catch (error) {
    console.error('保存设备失败:', error)
  } finally {
    saving.value = false
  }
}

const updateDeviceStats = () => {
  const stats = {
    switches: { total: 0, online: 0, offline: 0 },
    routers: { total: 0, online: 0, offline: 0 },
    firewalls: { total: 0, online: 0, offline: 0 },
    accessPoints: { total: 0, online: 0, offline: 0 }
  }

  devices.value.forEach((device) => {
    switch (device.type) {
      case 'switch':
        stats.switches.total++
        if (device.status === 'online') stats.switches.online++
        else stats.switches.offline++
        break
      case 'router':
        stats.routers.total++
        if (device.status === 'online') stats.routers.online++
        else stats.routers.offline++
        break
      case 'firewall':
        stats.firewalls.total++
        if (device.status === 'online') stats.firewalls.online++
        else stats.firewalls.offline++
        break
      case 'ap':
        stats.accessPoints.total++
        if (device.status === 'online') stats.accessPoints.online++
        else stats.accessPoints.offline++
        break
    }
  })

  Object.assign(deviceStats, stats)
}

const filterDevices = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredDevices 计算属性中处理
  console.log('搜索设备:', searchText.value)
}

const viewDeviceDetails = (device: any) => {
  console.log('查看设备详情:', device)
}

const configureDevice = (device: any) => {
  console.log('配置设备:', device)
}

const monitorDevice = (device: any) => {
  console.log('监控设备:', device)
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    switch: Network,
    router: Router,
    firewall: Shield,
    ap: Wifi
  }
  return icons[type] || Network
}

const getDeviceTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    ap: '无线AP'
  }
  return labels[type] || type
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-devices-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.devices-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 100%;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon-wrapper.switch {
  background: #e6f7ff;
  color: #1890ff;
}

.card-icon-wrapper.router {
  background: #f6ffed;
  color: #52c41a;
}

.card-icon-wrapper.firewall {
  background: #fff2e8;
  color: #fa8c16;
}

.card-icon-wrapper.ap {
  background: #f9f0ff;
  color: #722ed1;
}

.card-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.device-count {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.device-status {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.device-status .online {
  color: #52c41a;
}

.device-status .offline {
  color: #ff4d4f;
}

.devices-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.list-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  max-width: 100%;
}

.device-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.device-card.online {
  border-left: 4px solid #52c41a;
}

.device-card.offline {
  border-left: 4px solid #ff4d4f;
}

.device-card.warning {
  border-left: 4px solid #faad14;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.device-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.device-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.device-status-indicator.online {
  background: #52c41a;
}

.device-status-indicator.offline {
  background: #ff4d4f;
}

.device-status-indicator.warning {
  background: #faad14;
}

.device-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.device-info p {
  margin: 0 0 2px 0;
  color: #666;
  font-size: 14px;
}

.device-metrics {
  display: flex;
  justify-content: space-between;
  margin: 12px 0;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.device-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 20px;
  height: 20px;
}

.icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 搜索框样式修复 - 使用CSS变量适配主题 */
.custom-search-input {
  -webkit-app-region: no-drag;
}

.custom-search-input .ant-input {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  border: 1px solid var(--border-color, #d9d9d9) !important;
  color: var(--text-color, #000000) !important;
}

.custom-search-input .ant-input:hover {
  border-color: var(--input-focus-border, #40a9ff) !important;
}

.custom-search-input .ant-input:focus {
  border-color: var(--input-focus-border, #40a9ff) !important;
  box-shadow: var(--input-focus-shadow, 0 0 0 2px rgba(24, 144, 255, 0.2)) !important;
}

.custom-search-input .ant-input::placeholder {
  color: var(--text-color-tertiary, #bfbfbf) !important;
}

.custom-search-input .ant-input-search-button {
  background-color: var(--button-bg-color, #1890ff) !important;
  border-color: var(--button-bg-color, #1890ff) !important;
}

.custom-search-input .ant-input-search-button:hover {
  background-color: var(--button-hover-bg, #40a9ff) !important;
  border-color: var(--button-hover-bg, #40a9ff) !important;
}

/* 确保搜索框在所有主题下都能正常显示 */
.custom-search-input .ant-input-affix-wrapper {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  border: 1px solid var(--border-color, #d9d9d9) !important;
}

.custom-search-input .ant-input-affix-wrapper:hover {
  border-color: var(--input-focus-border, #40a9ff) !important;
}

.custom-search-input .ant-input-affix-wrapper-focused {
  border-color: var(--input-focus-border, #40a9ff) !important;
  box-shadow: var(--input-focus-shadow, 0 0 0 2px rgba(24, 144, 255, 0.2)) !important;
}

/* 强制覆盖任何可能的深色样式 */
.custom-search-input input,
.custom-search-input .ant-input-affix-wrapper input {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  color: var(--text-color, #262626) !important;
}

.custom-search-input .ant-input-affix-wrapper,
.custom-search-input .ant-input-group-wrapper .ant-input-affix-wrapper {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  border-color: var(--border-color, #d9d9d9) !important;
}

/* 额外的强制样式覆盖 */
.custom-search-input .ant-input-search .ant-input-affix-wrapper,
.custom-search-input .ant-input-search .ant-input {
  background-color: var(--globalInput-bg-color, #ffffff) !important;
  color: var(--text-color, #262626) !important;
  border-color: var(--border-color, #d9d9d9) !important;
}
</style>
