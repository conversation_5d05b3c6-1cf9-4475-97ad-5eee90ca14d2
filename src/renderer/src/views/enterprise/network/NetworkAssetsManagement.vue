<template>
  <div
    class="network-assets-management"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Server class="title-icon" />
            网络资产管理
          </h1>
          <p class="page-description">管理网络设备清单、配置备份和生命周期跟踪</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="exportAssets">
          <Download class="btn-icon" />
          导出清单
        </a-button>
        <a-button
          type="primary"
          @click="addAsset"
        >
          <Plus class="btn-icon" />
          添加资产
        </a-button>
      </div>
    </div>

    <!-- 资产概览 -->
    <div class="assets-overview">
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-header">
            <h3>总资产数量</h3>
            <div class="card-value">{{ assetStats.total }}</div>
          </div>
          <div class="card-breakdown">
            <div class="breakdown-item">
              <span class="breakdown-label">在线</span>
              <span class="breakdown-value online">{{ assetStats.online }}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">离线</span>
              <span class="breakdown-value offline">{{ assetStats.offline }}</span>
            </div>
            <div class="breakdown-item">
              <span class="breakdown-label">维护中</span>
              <span class="breakdown-value maintenance">{{ assetStats.maintenance }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>设备类型分布</h3>
          </div>
          <div class="type-distribution">
            <div
              v-for="type in deviceTypes"
              :key="type.name"
              class="type-item"
            >
              <div class="type-info">
                <span class="type-name">{{ type.name }}</span>
                <span class="type-count">{{ type.count }}</span>
              </div>
              <div class="type-bar">
                <div
                  class="type-fill"
                  :style="{ width: type.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>生命周期状态</h3>
          </div>
          <div class="lifecycle-status">
            <div class="lifecycle-item new">
              <span class="lifecycle-label">新设备</span>
              <span class="lifecycle-count">{{ lifecycleStats.new }}</span>
            </div>
            <div class="lifecycle-item active">
              <span class="lifecycle-label">使用中</span>
              <span class="lifecycle-count">{{ lifecycleStats.active }}</span>
            </div>
            <div class="lifecycle-item aging">
              <span class="lifecycle-label">老化</span>
              <span class="lifecycle-count">{{ lifecycleStats.aging }}</span>
            </div>
            <div class="lifecycle-item eol">
              <span class="lifecycle-label">报废</span>
              <span class="lifecycle-count">{{ lifecycleStats.eol }}</span>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-header">
            <h3>配置备份状态</h3>
          </div>
          <div class="backup-status">
            <div class="backup-metric">
              <span class="metric-label">已备份设备</span>
              <span class="metric-value">{{ backupStats.backedUp }}/{{ assetStats.total }}</span>
            </div>
            <div class="backup-metric">
              <span class="metric-label">最近备份</span>
              <span class="metric-value">{{ backupStats.lastBackup }}</span>
            </div>
            <div class="backup-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: backupStats.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ backupStats.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="assets-list">
      <div class="list-header">
        <h2 class="section-title">网络资产清单</h2>
        <div class="list-filters">
          <a-select
            v-model:value="selectedType"
            style="width: 120px"
          >
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedStatus"
            style="width: 100px"
          >
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索设备名称、IP或序列号"
            style="width: 250px"
            class="custom-search-input"
            @search="filterAssets"
          />
        </div>
      </div>

      <div class="assets-table">
        <a-table
          :columns="tableColumns"
          :data-source="filteredAssets"
          :loading="loading"
          :pagination="{ pageSize: 15 }"
          row-key="id"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusLabel(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'type'">
              <div class="type-cell">
                <component
                  :is="getDeviceIcon(record.type)"
                  class="type-icon"
                />
                <span>{{ getTypeLabel(record.type) }}</span>
              </div>
            </template>
            <template v-if="column.key === 'lifecycle'">
              <a-tag :color="getLifecycleColor(record.lifecycle)">
                {{ getLifecycleLabel(record.lifecycle) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'backup'">
              <div class="backup-cell">
                <div
                  class="backup-indicator"
                  :class="record.hasBackup ? 'backed-up' : 'no-backup'"
                >
                  <CheckCircle
                    v-if="record.hasBackup"
                    class="backup-icon"
                  />
                  <XCircle
                    v-else
                    class="backup-icon"
                  />
                </div>
                <span>{{ record.hasBackup ? '已备份' : '未备份' }}</span>
              </div>
            </template>
            <template v-if="column.key === 'actions'">
              <a-space>
                <a-button
                  size="small"
                  @click="viewAssetDetails(record)"
                >
                  <Eye class="btn-icon" />
                  详情
                </a-button>
                <a-button
                  size="small"
                  :disabled="!record.canBackup"
                  @click="backupConfig(record)"
                >
                  <Save class="btn-icon" />
                  备份
                </a-button>
                <a-button
                  size="small"
                  @click="editAsset(record)"
                >
                  <Edit class="btn-icon" />
                  编辑
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 设备详情对话框 -->
    <a-modal
      v-model:open="showAssetDetailModal"
      title="设备详情"
      width="800px"
      :footer="null"
    >
      <div
        v-if="selectedAsset"
        class="asset-detail"
      >
        <a-descriptions
          :column="2"
          bordered
        >
          <a-descriptions-item label="设备名称">
            {{ selectedAsset.name }}
          </a-descriptions-item>
          <a-descriptions-item label="设备类型">
            <div class="type-cell">
              <component
                :is="getDeviceIcon(selectedAsset.type)"
                class="type-icon"
              />
              <span>{{ getTypeLabel(selectedAsset.type) }}</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="IP地址">
            {{ selectedAsset.ip }}
          </a-descriptions-item>
          <a-descriptions-item label="序列号">
            {{ selectedAsset.serialNumber }}
          </a-descriptions-item>
          <a-descriptions-item label="MAC地址">
            {{ selectedAsset.macAddress || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="品牌型号">
            {{ selectedAsset.model || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="固件版本">
            {{ selectedAsset.firmwareVersion || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="安装位置">
            {{ selectedAsset.location }}
          </a-descriptions-item>
          <a-descriptions-item label="机柜信息">
            {{ selectedAsset.rackInfo || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备状态">
            <a-tag :color="getStatusColor(selectedAsset.status)">
              {{ getStatusLabel(selectedAsset.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="生命周期">
            <a-tag :color="getLifecycleColor(selectedAsset.lifecycle)">
              {{ getLifecycleLabel(selectedAsset.lifecycle) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="购买日期">
            {{ selectedAsset.purchaseDate || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="保修期限">
            {{ selectedAsset.warrantyExpiry || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            {{ selectedAsset.owner || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="支持备份">
            <a-tag :color="selectedAsset.canBackup ? 'green' : 'red'">
              {{ selectedAsset.canBackup ? '支持' : '不支持' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="备份状态">
            <div class="backup-cell">
              <div
                class="backup-indicator"
                :class="selectedAsset.hasBackup ? 'backed-up' : 'no-backup'"
              >
                <CheckCircle
                  v-if="selectedAsset.hasBackup"
                  class="backup-icon"
                />
                <XCircle
                  v-else
                  class="backup-icon"
                />
              </div>
              <span>{{ selectedAsset.hasBackup ? '已备份' : '未备份' }}</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="登录用户名">
            {{ selectedAsset.username || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="SSH端口">
            {{ selectedAsset.sshPort || 22 }}
          </a-descriptions-item>
          <a-descriptions-item label="SNMP团体名">
            {{ selectedAsset.snmpCommunity || 'public' }}
          </a-descriptions-item>
          <a-descriptions-item
            label="最后更新"
            :span="2"
          >
            {{ selectedAsset.lastUpdate }}
          </a-descriptions-item>
          <a-descriptions-item
            label="描述"
            :span="2"
          >
            {{ selectedAsset.description || '无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 编辑资产对话框 -->
    <a-modal
      v-model:open="showEditAssetModal"
      title="编辑网络资产"
      width="700px"
      :footer="null"
    >
      <a-form
        :model="editAssetForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="updateAsset"
      >
        <a-form-item
          label="设备名称"
          name="name"
          :rules="[{ required: true, message: '请输入设备名称' }]"
        >
          <a-input
            v-model:value="editAssetForm.name"
            placeholder="请输入设备名称"
          />
        </a-form-item>

        <a-form-item
          label="设备类型"
          name="type"
          :rules="[{ required: true, message: '请选择设备类型' }]"
        >
          <a-select
            v-model:value="editAssetForm.type"
            placeholder="请选择设备类型"
          >
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="storage">存储设备</a-select-option>
            <a-select-option value="load-balancer">负载均衡器</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="IP地址"
          name="ip"
          :rules="[
            { required: true, message: '请输入IP地址' },
            { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' }
          ]"
        >
          <a-input
            v-model:value="editAssetForm.ip"
            placeholder="请输入IP地址"
          />
        </a-form-item>

        <a-form-item
          label="序列号"
          name="serialNumber"
          :rules="[{ required: true, message: '请输入序列号' }]"
        >
          <a-input
            v-model:value="editAssetForm.serialNumber"
            placeholder="请输入设备序列号"
          />
        </a-form-item>

        <a-form-item
          label="安装位置"
          name="location"
          :rules="[{ required: true, message: '请输入安装位置' }]"
        >
          <a-input
            v-model:value="editAssetForm.location"
            placeholder="请输入安装位置"
          />
        </a-form-item>

        <a-form-item
          label="设备状态"
          name="status"
        >
          <a-select
            v-model:value="editAssetForm.status"
            placeholder="请选择设备状态"
          >
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入登录用户名' }]"
        >
          <a-input
            v-model:value="editAssetForm.username"
            placeholder="请输入设备登录用户名"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入登录密码' }]"
        >
          <a-input-password
            v-model:value="editAssetForm.password"
            placeholder="请输入设备登录密码"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeEditAssetModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '更新中...' : '更新' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加资产对话框 -->
    <a-modal
      v-model:open="showAddAssetModal"
      title="添加网络资产"
      width="700px"
      :footer="null"
    >
      <a-form
        :model="assetForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveAsset"
      >
        <a-form-item
          label="设备名称"
          name="name"
          :rules="[{ required: true, message: '请输入设备名称' }]"
        >
          <a-input
            v-model:value="assetForm.name"
            placeholder="请输入设备名称"
          />
        </a-form-item>

        <a-form-item
          label="设备类型"
          name="type"
          :rules="[{ required: true, message: '请选择设备类型' }]"
        >
          <a-select
            v-model:value="assetForm.type"
            placeholder="请选择设备类型"
            @change="onAssetTypeChange"
          >
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="storage">存储设备</a-select-option>
            <a-select-option value="load-balancer">负载均衡器</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="IP地址"
          name="ip"
          :rules="[
            { required: true, message: '请输入IP地址' },
            { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' }
          ]"
        >
          <a-input
            v-model:value="assetForm.ip"
            placeholder="请输入IP地址"
          />
        </a-form-item>

        <a-form-item
          label="序列号"
          name="serialNumber"
          :rules="[{ required: true, message: '请输入序列号' }]"
        >
          <a-input
            v-model:value="assetForm.serialNumber"
            placeholder="请输入设备序列号"
          />
        </a-form-item>

        <a-form-item
          label="MAC地址"
          name="macAddress"
        >
          <a-input
            v-model:value="assetForm.macAddress"
            placeholder="请输入MAC地址 (如: AA:BB:CC:DD:EE:FF)"
          />
        </a-form-item>

        <a-form-item
          label="品牌型号"
          name="model"
        >
          <a-input
            v-model:value="assetForm.model"
            placeholder="请输入品牌和型号"
          />
        </a-form-item>

        <a-form-item
          label="固件版本"
          name="firmwareVersion"
        >
          <a-input
            v-model:value="assetForm.firmwareVersion"
            placeholder="请输入固件版本"
          />
        </a-form-item>

        <a-form-item
          label="安装位置"
          name="location"
          :rules="[{ required: true, message: '请输入安装位置' }]"
        >
          <a-input
            v-model:value="assetForm.location"
            placeholder="请输入安装位置"
          />
        </a-form-item>

        <a-form-item
          label="机柜信息"
          name="rackInfo"
        >
          <a-input
            v-model:value="assetForm.rackInfo"
            placeholder="机柜编号和位置"
          />
        </a-form-item>

        <a-form-item
          label="设备状态"
          name="status"
        >
          <a-select
            v-model:value="assetForm.status"
            placeholder="请选择设备状态"
          >
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="生命周期"
          name="lifecycle"
        >
          <a-select
            v-model:value="assetForm.lifecycle"
            placeholder="请选择生命周期状态"
          >
            <a-select-option value="new">新设备</a-select-option>
            <a-select-option value="active">使用中</a-select-option>
            <a-select-option value="aging">老化</a-select-option>
            <a-select-option value="eol">报废</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="购买日期"
          name="purchaseDate"
        >
          <a-date-picker
            v-model:value="assetForm.purchaseDate"
            placeholder="请选择购买日期"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="保修期限"
          name="warrantyExpiry"
        >
          <a-date-picker
            v-model:value="assetForm.warrantyExpiry"
            placeholder="请选择保修期限"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="负责人"
          name="owner"
        >
          <a-input
            v-model:value="assetForm.owner"
            placeholder="设备负责人"
          />
        </a-form-item>

        <a-form-item
          label="支持备份"
          name="canBackup"
        >
          <a-switch
            v-model:checked="assetForm.canBackup"
            checked-children="支持"
            un-checked-children="不支持"
          />
        </a-form-item>

        <!-- 登录信息分组 -->
        <a-divider orientation="left">登录信息</a-divider>

        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入登录用户名' }]"
        >
          <a-input
            v-model:value="assetForm.username"
            placeholder="请输入设备登录用户名"
          />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入登录密码' }]"
        >
          <a-input-password
            v-model:value="assetForm.password"
            placeholder="请输入设备登录密码"
          />
        </a-form-item>

        <a-form-item
          label="SSH端口"
          name="sshPort"
        >
          <a-input-number
            v-model:value="assetForm.sshPort"
            :min="1"
            :max="65535"
            placeholder="22"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="SNMP团体名"
          name="snmpCommunity"
        >
          <a-input
            v-model:value="assetForm.snmpCommunity"
            placeholder="public"
          />
        </a-form-item>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="assetForm.description"
            :rows="3"
            placeholder="设备描述信息"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeAddAssetModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Server,
  RefreshCw,
  Download,
  Plus,
  Eye,
  Save,
  Edit,
  CheckCircle,
  XCircle,
  Network,
  Router,
  Shield,
  Wifi,
  HardDrive
} from 'lucide-vue-next'
import { minioBackupService, generateBackupFileName } from '@/services/minioBackupService'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedType = ref('all')
const selectedStatus = ref('all')
const searchText = ref('')
const showAddAssetModal = ref(false)
const showAssetDetailModal = ref(false)
const showEditAssetModal = ref(false)
const saving = ref(false)
const selectedAsset = ref(null)
const editAssetForm = ref({
  id: 0,
  name: '',
  type: '',
  ip: '',
  serialNumber: '',
  location: '',
  status: 'online',
  username: '',
  password: ''
})

// 资产表单数据
const assetForm = ref({
  name: '',
  type: '',
  ip: '',
  serialNumber: '',
  macAddress: '',
  model: '',
  firmwareVersion: '',
  location: '',
  rackInfo: '',
  status: 'online',
  lifecycle: 'new',
  purchaseDate: null,
  warrantyExpiry: null,
  owner: '',
  canBackup: true,
  description: '',
  // 登录信息
  username: '',
  password: '',
  sshPort: 22,
  snmpCommunity: 'public'
})

// 资产统计数据
const assetStats = reactive({
  total: 156,
  online: 142,
  offline: 8,
  maintenance: 6
})

// 设备类型分布
const deviceTypes = ref([
  { name: '交换机', count: 45, percentage: 29 },
  { name: '服务器', count: 38, percentage: 24 },
  { name: '无线AP', count: 32, percentage: 21 },
  { name: '路由器', count: 25, percentage: 16 },
  { name: '防火墙', count: 16, percentage: 10 }
])

// 生命周期统计
const lifecycleStats = reactive({
  new: 12,
  active: 128,
  aging: 14,
  eol: 2
})

// 备份统计
const backupStats = reactive({
  backedUp: 134,
  lastBackup: '2小时前',
  percentage: 86
})

// 表格列配置
const tableColumns = [
  { title: '设备名称', dataIndex: 'name', key: 'name', width: 150, fixed: 'left' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: 'IP地址', dataIndex: 'ip', key: 'ip', width: 130 },
  { title: '序列号', dataIndex: 'serialNumber', key: 'serialNumber', width: 150 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '生命周期', dataIndex: 'lifecycle', key: 'lifecycle', width: 100 },
  { title: '位置', dataIndex: 'location', key: 'location', width: 120 },
  { title: '配置备份', dataIndex: 'backup', key: 'backup', width: 120 },
  { title: '最后更新', dataIndex: 'lastUpdate', key: 'lastUpdate', width: 150 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' }
]

// 资产数据
const assets = ref([
  {
    id: 1,
    name: 'Core-Switch-01',
    type: 'switch',
    ip: '************',
    serialNumber: 'SW001234567',
    status: 'online',
    lifecycle: 'active',
    location: '机房A-机柜01',
    hasBackup: true,
    canBackup: true,
    lastUpdate: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    name: 'Web-Server-01',
    type: 'server',
    ip: '************0',
    serialNumber: 'SV001234567',
    status: 'online',
    lifecycle: 'active',
    location: '机房A-机柜03',
    hasBackup: true,
    canBackup: true,
    lastUpdate: '2024-01-15 14:25:18'
  },
  {
    id: 3,
    name: 'Edge-Router-01',
    type: 'router',
    ip: '***********',
    serialNumber: 'RT001234567',
    status: 'online',
    lifecycle: 'aging',
    location: '机房A-机柜01',
    hasBackup: false,
    canBackup: true,
    lastUpdate: '2024-01-15 14:20:12'
  },
  {
    id: 4,
    name: 'Firewall-Main',
    type: 'firewall',
    ip: '*************',
    serialNumber: 'FW001234567',
    status: 'maintenance',
    lifecycle: 'active',
    location: '机房A-机柜02',
    hasBackup: true,
    canBackup: false,
    lastUpdate: '2024-01-15 13:45:30'
  },
  {
    id: 5,
    name: 'AP-Floor1-01',
    type: 'ap',
    ip: '*************',
    serialNumber: 'AP001234567',
    status: 'offline',
    lifecycle: 'active',
    location: '1楼大厅',
    hasBackup: false,
    canBackup: false,
    lastUpdate: '2024-01-15 12:30:45'
  }
])

// 过滤后的资产列表
const filteredAssets = computed(() => {
  let filtered = assets.value

  if (selectedType.value !== 'all') {
    filtered = filtered.filter((asset) => asset.type === selectedType.value)
  }

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((asset) => asset.status === selectedStatus.value)
  }

  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(
      (asset) => asset.name.toLowerCase().includes(search) || asset.ip.includes(search) || asset.serialNumber.toLowerCase().includes(search)
    )
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const exportAssets = () => {
  console.log('导出资产清单')
}

const addAsset = () => {
  showAddAssetModal.value = true
  resetAssetForm()
}

const closeAddAssetModal = () => {
  showAddAssetModal.value = false
  resetAssetForm()
}

const resetAssetForm = () => {
  assetForm.value = {
    name: '',
    type: '',
    ip: '',
    serialNumber: '',
    macAddress: '',
    model: '',
    firmwareVersion: '',
    location: '',
    rackInfo: '',
    status: 'online',
    lifecycle: 'new',
    purchaseDate: null,
    warrantyExpiry: null,
    owner: '',
    canBackup: true,
    description: '',
    // 登录信息
    username: '',
    password: '',
    sshPort: 22,
    snmpCommunity: 'public'
  }
}

const onAssetTypeChange = (type: string) => {
  // 根据设备类型设置一些默认值
  const typeDefaults: Record<string, any> = {
    switch: { canBackup: true },
    router: { canBackup: true },
    firewall: { canBackup: true },
    ap: { canBackup: false },
    server: { canBackup: false },
    storage: { canBackup: false },
    'load-balancer': { canBackup: true }
  }

  if (typeDefaults[type]) {
    Object.assign(assetForm.value, typeDefaults[type])
  }
}

const saveAsset = async () => {
  saving.value = true

  try {
    // 模拟保存资产
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建新资产对象
    const newAsset = {
      id: Date.now(),
      name: assetForm.value.name,
      type: assetForm.value.type,
      ip: assetForm.value.ip,
      serialNumber: assetForm.value.serialNumber,
      macAddress: assetForm.value.macAddress,
      model: assetForm.value.model,
      firmwareVersion: assetForm.value.firmwareVersion,
      location: assetForm.value.location,
      rackInfo: assetForm.value.rackInfo,
      status: assetForm.value.status,
      lifecycle: assetForm.value.lifecycle,
      purchaseDate: assetForm.value.purchaseDate ? assetForm.value.purchaseDate.format('YYYY-MM-DD') : null,
      warrantyExpiry: assetForm.value.warrantyExpiry ? assetForm.value.warrantyExpiry.format('YYYY-MM-DD') : null,
      owner: assetForm.value.owner,
      hasBackup: false,
      canBackup: assetForm.value.canBackup,
      description: assetForm.value.description,
      // 登录信息
      username: assetForm.value.username,
      password: assetForm.value.password,
      sshPort: assetForm.value.sshPort,
      snmpCommunity: assetForm.value.snmpCommunity,
      lastUpdate: new Date().toLocaleString('zh-CN')
    }

    // 添加到资产列表
    assets.value.push(newAsset)

    // 更新统计数据
    updateAssetStats()

    // 关闭对话框
    closeAddAssetModal()

    // 显示成功消息
    console.log('资产添加成功:', newAsset)
  } catch (error) {
    console.error('保存资产失败:', error)
  } finally {
    saving.value = false
  }
}

const updateAssetStats = () => {
  // 重新计算资产统计数据
  const stats = {
    total: assets.value.length,
    online: 0,
    offline: 0,
    maintenance: 0
  }

  assets.value.forEach((asset) => {
    if (asset.status === 'online') {
      stats.online++
    } else if (asset.status === 'offline') {
      stats.offline++
    } else if (asset.status === 'maintenance') {
      stats.maintenance++
    }
  })

  // 更新统计数据
  Object.assign(assetStats, stats)

  // 更新设备类型分布
  const typeCount: Record<string, number> = {}
  assets.value.forEach((asset) => {
    const typeName = getTypeLabel(asset.type)
    typeCount[typeName] = (typeCount[typeName] || 0) + 1
  })

  const total = assets.value.length
  deviceTypes.value = Object.entries(typeCount).map(([name, count]) => ({
    name,
    count,
    percentage: Math.round((count / total) * 100)
  }))

  // 更新生命周期统计
  const lifecycleCount = {
    new: 0,
    active: 0,
    aging: 0,
    eol: 0
  }

  assets.value.forEach((asset) => {
    if (lifecycleCount[asset.lifecycle] !== undefined) {
      lifecycleCount[asset.lifecycle]++
    }
  })

  Object.assign(lifecycleStats, lifecycleCount)

  // 更新备份统计
  const backedUpCount = assets.value.filter((asset) => asset.hasBackup).length
  backupStats.backedUp = backedUpCount
  backupStats.percentage = Math.round((backedUpCount / total) * 100)
}

const filterAssets = () => {
  // 过滤逻辑已在computed中处理
}

const viewAssetDetails = (asset: any) => {
  selectedAsset.value = asset
  showAssetDetailModal.value = true
}

const backupConfig = async (asset: any) => {
  try {
    console.log(`开始备份设备 ${asset.name} 的配置...`)

    // 生成配置数据
    const configData = generateMockConfig(asset)

    // 生成备份文件名
    const fileName = generateBackupFileName(asset.name, asset.ip)

    // 准备备份数据
    const backupData = {
      deviceId: asset.id,
      deviceName: asset.name,
      deviceType: asset.type,
      ip: asset.ip,
      backupTime: new Date().toISOString(),
      configData,
      fileName
    }

    // 上传到MinIO
    const objectName = await minioBackupService.uploadBackup(backupData)
    console.log('配置备份已上传到MinIO:', objectName)

    // 更新资产备份状态
    const assetIndex = assets.value.findIndex((a) => a.id === asset.id)
    if (assetIndex !== -1) {
      assets.value[assetIndex].hasBackup = true
      assets.value[assetIndex].lastUpdate = new Date().toLocaleString('zh-CN')
    }

    // 更新统计数据
    updateAssetStats()

    console.log('配置备份成功:', backupData)
  } catch (error) {
    console.error('备份配置失败:', error)
  }
}

const generateMockConfig = (asset: any) => {
  // 生成模拟的设备配置数据
  return `! Configuration for ${asset.name}
! Generated on ${new Date().toLocaleString()}
!
hostname ${asset.name}
!
interface GigabitEthernet0/1
 ip address ${asset.ip} *************
 no shutdown
!
snmp-server community ${asset.snmpCommunity || 'public'} RO
!
line vty 0 4
 login local
 transport input ssh
!
end`
}

const editAsset = (asset: any) => {
  editAssetForm.value = { ...asset }
  showEditAssetModal.value = true
}

const closeEditAssetModal = () => {
  showEditAssetModal.value = false
  editAssetForm.value = {
    id: 0,
    name: '',
    type: '',
    ip: '',
    serialNumber: '',
    location: '',
    status: 'online',
    username: '',
    password: ''
  }
}

const updateAsset = async () => {
  saving.value = true

  try {
    // 模拟更新资产
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新资产列表中的数据
    const assetIndex = assets.value.findIndex((a) => a.id === editAssetForm.value.id)
    if (assetIndex !== -1) {
      assets.value[assetIndex] = {
        ...assets.value[assetIndex],
        ...editAssetForm.value,
        lastUpdate: new Date().toLocaleString('zh-CN')
      }
    }

    // 更新统计数据
    updateAssetStats()

    // 关闭对话框
    closeEditAssetModal()

    console.log('资产更新成功:', editAssetForm.value)
  } catch (error) {
    console.error('更新资产失败:', error)
  } finally {
    saving.value = false
  }
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    online: 'green',
    offline: 'red',
    maintenance: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中'
  }
  return labels[status] || status
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    ap: '无线AP',
    server: '服务器'
  }
  return labels[type] || type
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    switch: Network,
    router: Router,
    firewall: Shield,
    ap: Wifi,
    server: HardDrive
  }
  return icons[type] || Server
}

const getLifecycleColor = (lifecycle: string) => {
  const colors: Record<string, string> = {
    new: 'blue',
    active: 'green',
    aging: 'orange',
    eol: 'red'
  }
  return colors[lifecycle] || 'default'
}

const getLifecycleLabel = (lifecycle: string) => {
  const labels: Record<string, string> = {
    new: '新设备',
    active: '使用中',
    aging: '老化',
    eol: '报废'
  }
  return labels[lifecycle] || lifecycle
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-assets-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.assets-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 16px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 32px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-breakdown {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.breakdown-item {
  text-align: center;
}

.breakdown-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.breakdown-value {
  font-size: 16px;
  font-weight: 600;
}

.breakdown-value.online {
  color: #52c41a;
}

.breakdown-value.offline {
  color: #ff4d4f;
}

.breakdown-value.maintenance {
  color: #faad14;
}

.type-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.type-name {
  color: #1a1a1a;
  font-weight: 500;
}

.type-count {
  color: #666;
}

.type-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.type-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.lifecycle-status {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.lifecycle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.lifecycle-item.new {
  background: #e6f7ff;
  color: #1890ff;
}

.lifecycle-item.active {
  background: #f6ffed;
  color: #52c41a;
}

.lifecycle-item.aging {
  background: #fffbe6;
  color: #faad14;
}

.lifecycle-item.eol {
  background: #fff2f0;
  color: #ff4d4f;
}

.lifecycle-label {
  font-weight: 500;
}

.lifecycle-count {
  font-weight: 600;
}

.backup-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 600;
  color: #1a1a1a;
}

.backup-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #52c41a;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.assets-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.list-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.type-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  width: 16px;
  height: 16px;
  color: #666;
}

.backup-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-indicator {
  display: flex;
  align-items: center;
}

.backup-indicator.backed-up {
  color: #52c41a;
}

.backup-indicator.no-backup {
  color: #ff4d4f;
}

.backup-icon {
  width: 16px;
  height: 16px;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 表单样式修复 */
.ant-modal .ant-form {
  -webkit-app-region: no-drag;
}

.ant-modal .ant-form-item {
  -webkit-app-region: no-drag;
}

.ant-modal .ant-input,
.ant-modal .ant-input-password,
.ant-modal .ant-select,
.ant-modal .ant-input-number,
.ant-modal .ant-textarea,
.ant-modal .ant-date-picker,
.ant-modal .ant-switch {
  -webkit-app-region: no-drag;
}

.ant-modal .ant-select-selector {
  -webkit-app-region: no-drag;
}

.ant-modal .ant-btn {
  -webkit-app-region: no-drag;
}

/* 修复模态框关闭按钮点击问题 */
.ant-modal-header {
  -webkit-app-region: no-drag;
}

.ant-modal-close {
  -webkit-app-region: no-drag !important;
  z-index: 1000;
  position: relative;
}

.ant-modal-close-x {
  -webkit-app-region: no-drag !important;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.ant-modal-title {
  -webkit-app-region: no-drag;
}

/* 确保整个模态框内容区域都可以正常交互 */
.ant-modal-content {
  -webkit-app-region: no-drag;
}

.ant-modal-body {
  -webkit-app-region: no-drag;
}

/* 搜索框样式修复 - 使用CSS变量适配主题 */
.custom-search-input {
  -webkit-app-region: no-drag;
}

.custom-search-input .ant-input {
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.custom-search-input .ant-input:hover {
  border-color: var(--input-focus-border) !important;
}

.custom-search-input .ant-input:focus {
  border-color: var(--input-focus-border) !important;
  box-shadow: var(--input-focus-shadow) !important;
}

.custom-search-input .ant-input::placeholder {
  color: var(--text-color-tertiary) !important;
}

.custom-search-input .ant-input-search-button {
  background-color: var(--button-bg-color) !important;
  border-color: var(--button-bg-color) !important;
}

.custom-search-input .ant-input-search-button:hover {
  background-color: var(--button-hover-bg) !important;
  border-color: var(--button-hover-bg) !important;
}

/* 确保搜索框在所有主题下都能正常显示 */
.list-filters .custom-search-input .ant-input {
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.list-filters .custom-search-input .ant-input-affix-wrapper {
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color) !important;
}

.list-filters .custom-search-input .ant-input-affix-wrapper:hover {
  border-color: var(--input-focus-border) !important;
}

.list-filters .custom-search-input .ant-input-affix-wrapper-focused {
  border-color: var(--input-focus-border) !important;
  box-shadow: var(--input-focus-shadow) !important;
}

/* 强制覆盖任何可能的深色样式 */
.custom-search-input input,
.custom-search-input .ant-input-affix-wrapper input {
  background-color: var(--bg-color-secondary) !important;
  color: var(--text-color) !important;
}

.custom-search-input .ant-input-affix-wrapper,
.custom-search-input .ant-input-group-wrapper .ant-input-affix-wrapper {
  background-color: var(--bg-color-secondary) !important;
  border-color: var(--border-color) !important;
}
</style>
